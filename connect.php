<?php

include_once "functions.php";

$host = "localhost";
$dbname = "ecommerce";
$username = "root";
$password = "";

try {
    $con = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $con->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    // echo "تم الاتصال بنجاح!"; // تم تعطيل هذا السطر لتجنب إظهار الرسالة في كل صفحة
} catch (PDOException $e) {
    echo "خطأ في الاتصال: " . $e->getMessage();
}
?>